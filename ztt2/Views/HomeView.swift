//
//  HomeView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 首页视图 - 家庭成员管理
 */
struct HomeView: View {

    // MARK: - Properties
    let onMemberSelected: (String) -> Void

    @StateObject private var viewModel = HomeViewModel()
    @State private var pageAppeared = false
    @State private var logoRotation: Double = 0
    @State private var isDeleteMode = false
    @State private var showAddMemberForm = false
    @State private var showFamilyOperationOptions = false
    @State private var showFamilyOperationForm = false
    @State private var showFamilyTotalScore = false
    @State private var showDateRangePicker = false
    @State private var showLotteryConfigDialog = false
    @State private var lotteryConfigPressed = false
    @State private var familyOperationType: FamilyOperationType = .add
    @State private var selectedDateRange: DateRangeType = .thisMonth

    // 计算属性：将Core Data的Member转换为FamilyMemberGridView需要的格式
    private var gridMembers: [FamilyMemberGridView.FamilyMember] {
        viewModel.members.map { member in
            FamilyMemberGridView.FamilyMember(
                id: member.objectID.uriRepresentation().absoluteString,
                name: member.displayName,
                role: member.role ?? "other",
                currentPoints: Int(member.currentPoints)
            )
        }
    }

    private var totalScore: Int {
        viewModel.getTotalScoreForDateRange(selectedDateRange)
    }

    var body: some View {
        ZStack {
            // 美化背景渐变
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)

            // 装饰性背景元素
            VStack {
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -20)
                }
                Spacer()
                HStack {
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 80, height: 80)
                        .offset(x: -30, y: 50)
                    Spacer()
                }
            }

            VStack(spacing: 0) {
                // 顶部区域 - Logo和配置按钮
                HStack {
                    // Logo with enhanced visual effects
                    ZStack {
                        // 背景渐变圆形
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#FFE49E").opacity(0.3),
                                        Color(hex: "#B5E36B").opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 110, height: 110)
                            .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 15, x: 0, y: 5)

                        // Logo图片
                        Image("logo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 90, height: 90)
                            .rotationEffect(.degrees(logoRotation))
                            .onTapGesture {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    logoRotation += 360
                                }
                            }
                    }
                    .offset(y: -20)

                    Spacer()

                    // 调试按钮组
                    HStack(spacing: 8) {
                        // 数据库测试按钮
                        Button(action: {
                            print("开始数据库测试...")
                            DataManager.shared.getDatabaseInfo()
                            DataManager.shared.testDatabaseConnection()
                        }) {
                            Image(systemName: "externaldrive.badge.checkmark")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(Color(hex: "#a9d051"))
                                .frame(width: 28, height: 28)
                                .background(
                                    Circle()
                                        .fill(Color(hex: "#f8ffe5"))
                                        .shadow(color: Color(hex: "#a9d051").opacity(0.2), radius: 3, x: 0, y: 1)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())

                        // 重置数据库按钮
                        Button(action: {
                            print("重置数据库...")
                            DataManager.shared.resetDatabase()
                        }) {
                            Image(systemName: "trash.circle")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(Color.red)
                                .frame(width: 28, height: 28)
                                .background(
                                    Circle()
                                        .fill(Color.red.opacity(0.1))
                                        .shadow(color: Color.red.opacity(0.2), radius: 3, x: 0, y: 1)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .offset(y: -20)

                    // 抽奖道具配置按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            lotteryConfigPressed = true
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            lotteryConfigPressed = false
                            handleLotteryConfig()
                        }
                    }) {
                        ZStack {
                            // 美化背景容器
                            RoundedRectangle(cornerRadius: 16)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#f8ffe5"),
                                            Color(hex: "#edf6d9")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 44)
                                .shadow(color: Color(hex: "#a9d051").opacity(0.25), radius: lotteryConfigPressed ? 10 : 6, x: 0, y: lotteryConfigPressed ? 4 : 2)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
                                )

                            // 装饰圆点
                            HStack {
                                Spacer()
                                VStack {
                                    Circle()
                                        .fill(Color(hex: "#a9d051").opacity(0.15))
                                        .frame(width: 20, height: 20)
                                        .offset(x: 8, y: -8)
                                    Spacer()
                                }
                            }

                            // 内容
                            HStack(spacing: 6) {
                                // 图标
                                Image("shezhi")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 20, height: 20)
                                    .foregroundColor(Color(hex: "#a9d051"))

                                // 文字
                                Text("抽奖配置")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(Color(hex: "#a9d051"))
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.8)
                            }

                            // 按压闪烁效果
                            if lotteryConfigPressed {
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.4))
                                    .frame(width: 120, height: 44)
                                    .transition(.opacity)
                            }
                        }
                        .frame(width: 120, height: 44)
                        .scaleEffect(lotteryConfigPressed ? 0.95 : 1.0)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .offset(y: -20)
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 5)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : -50)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)

                Spacer()
                    .frame(height: 5)

                // 操作按钮区域
                ActionButtonsView(
                    totalScore: totalScore,
                    dateRangeText: selectedDateRange.displayText,
                    onAddMemberTapped: {
                        handleAddMember()
                    },
                    onFamilyOperationTapped: {
                        handleFamilyOperation()
                    },
                    onTotalScoreTapped: {
                        handleTotalScoreTapped()
                    }
                )
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: pageAppeared)

                Spacer()
                    .frame(height: 20)

                // 家庭成员网格视图
                FamilyMemberGridView(
                    members: gridMembers,
                    isDeleteMode: isDeleteMode,
                    onMemberTapped: { member in
                        handleMemberTapped(member)
                    },
                    onEnterDeleteMode: {
                        enterDeleteMode()
                    },
                    onExitDeleteMode: {
                        exitDeleteMode()
                    },
                    onDeleteRequested: { member in
                        requestDeleteMember(member)
                    },
                    onRefresh: {
                        await refreshData()
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.7), value: pageAppeared)
            }
        }
        .onTapGesture {
            // 点击空白区域退出删除模式
            if isDeleteMode {
                exitDeleteMode()
            }
        }
        .onAppear {
            // Logo入场动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2)) {
                logoRotation = 0
            }

            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
        }
        .overlay(
            // 添加成员表单弹窗
            AddMemberFormView(
                isPresented: $showAddMemberForm,
                onSubmit: { memberData in
                    handleAddMemberSubmit(memberData)
                },
                onCancel: {
                    showAddMemberForm = false
                }
            )
        )
        .overlay(
            // 全家操作选项弹窗
            FamilyOperationOptionsView(
                isPresented: $showFamilyOperationOptions,
                onAddPoints: {
                    familyOperationType = .add
                    showFamilyOperationForm = true
                },
                onDeductPoints: {
                    familyOperationType = .deduct
                    showFamilyOperationForm = true
                }
            )
        )
        .overlay(
            // 全家操作表单弹窗
            FamilyOperationFormView(
                isPresented: $showFamilyOperationForm,
                operationType: familyOperationType,
                onSubmit: { name, value in
                    handleFamilyOperationSubmit(name: name, value: value, type: familyOperationType)
                },
                onCancel: {
                    showFamilyOperationForm = false
                }
            )
        )
        .overlay(
            // 日期范围选择器弹窗
            Group {
                if showDateRangePicker {
                    DateRangePickerView(
                        selectedDateRange: $selectedDateRange,
                        isPresented: $showDateRangePicker
                    )
                    .transition(.opacity)
                }
            }
        )
        .alert("抽奖道具配置", isPresented: $showLotteryConfigDialog) {
            Button("确定") {
                // TODO: 实现抽奖道具配置功能
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("抽奖道具配置功能开发中...")
        }
    }

    // MARK: - Action Handlers

    /**
     * 处理添加成员按钮点击
     */
    private func handleAddMember() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showAddMemberForm = true
        }
    }

    /**
     * 处理全家操作按钮点击
     */
    private func handleFamilyOperation() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showFamilyOperationOptions = true
        }
    }

    /**
     * 处理抽奖道具配置按钮点击
     */
    private func handleLotteryConfig() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showLotteryConfigDialog = true
        }
    }

    /**
     * 处理全家总分按钮点击
     */
    private func handleTotalScoreTapped() {
        print("显示时间范围选择弹窗")
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            showDateRangePicker = true
        }
    }

    /**
     * 处理添加成员表单提交
     */
    private func handleAddMemberSubmit(_ memberData: MemberFormData) {
        print("添加成员: \(memberData.name), 初始积分: \(memberData.initialPointsValue), 角色: \(memberData.role)")

        // 调用ViewModel添加成员
        viewModel.addMember(
            name: memberData.name,
            role: memberData.role,
            birthDate: memberData.birthDate,
            initialPoints: memberData.initialPointsValue
        )

        showAddMemberForm = false
    }

    /**
     * 处理全家操作表单提交
     */
    private func handleFamilyOperationSubmit(name: String, value: Int, type: FamilyOperationType) {
        let operationText = type == .add ? "全家加分" : "全家扣分"
        print("\(operationText): \(name), 分值: \(value)")

        // 调用ViewModel进行全家操作
        if type == .add {
            viewModel.addPointsToAllMembers(reason: name, value: value)
        } else {
            viewModel.deductPointsFromAllMembers(reason: name, value: value)
        }

        showFamilyOperationForm = false
    }

    /**
     * 处理家庭成员卡片点击
     */
    private func handleMemberTapped(_ member: FamilyMemberGridView.FamilyMember) {
        print("点击了家庭成员: \(member.name)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }

        // 调用导航回调，传递成员ID
        onMemberSelected(member.id)
    }

    /**
     * 进入删除模式
     */
    private func enterDeleteMode() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isDeleteMode = true
        }
    }

    /**
     * 退出删除模式
     */
    private func exitDeleteMode() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isDeleteMode = false
        }
    }

    /**
     * 请求删除成员
     */
    private func requestDeleteMember(_ member: FamilyMemberGridView.FamilyMember) {
        print("请求删除成员: \(member.name)")

        // 根据ID找到对应的Core Data Member对象
        if let coreDataMember = viewModel.members.first(where: {
            $0.objectID.uriRepresentation().absoluteString == member.id
        }) {
            // 调用ViewModel删除成员
            viewModel.deleteMember(coreDataMember)
        }

        // 退出删除模式
        exitDeleteMode()
    }



    /**
     * 刷新数据
     */
    private func refreshData() async {
        print("下拉刷新数据")
        await MainActor.run {
            viewModel.refresh()
        }
    }
}

// MARK: - Preview
#Preview {
    HomeView { memberId in
        print("选中成员: \(memberId)")
    }
}
