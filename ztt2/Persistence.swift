//
//  Persistence.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import CoreData
import CloudKit

struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例用户
        let user = User(context: viewContext)
        user.id = UUID()
        user.nickname = "示例用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()

        // 创建示例订阅
        let subscription = Subscription(context: viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        // 创建示例家庭成员
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "ztt2")

        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            // 配置持久化存储描述
            if let storeDescription = container.persistentStoreDescriptions.first {
                // 禁用持久化历史记录跟踪，避免权限问题
                storeDescription.setOption(false as NSNumber, forKey: NSPersistentHistoryTrackingKey)
                storeDescription.setOption(false as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

                // 确保自动迁移
                storeDescription.setOption(true as NSNumber, forKey: NSMigratePersistentStoresAutomaticallyOption)
                storeDescription.setOption(true as NSNumber, forKey: NSInferMappingModelAutomaticallyOption)

                print("Core Data store URL: \(storeDescription.url?.path ?? "Unknown")")
            }
        }

        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                /*
                 典型的错误原因包括：
                 * 父目录不存在、无法创建或不允许写入
                 * 由于权限或设备锁定时的数据保护，无法访问持久存储
                 * 设备空间不足
                 * 存储无法迁移到当前模型版本
                 * CloudKit 配置问题
                 检查错误消息以确定实际问题
                 */
                print("Core Data error: \(error), \(error.userInfo)")

                // 如果是权限错误，尝试删除数据库文件重新创建
                if error.code == 513 { // NSCocoaErrorDomain Code=513
                    print("检测到权限错误，尝试重置数据库...")
                    // 注意：在闭包中不能调用mutating方法，这里只记录错误
                    print("需要手动重置数据库文件")
                    return
                }

                fatalError("Unresolved error \(error), \(error.userInfo)")
            } else {
                print("Core Data store loaded successfully: \(storeDescription.url?.path ?? "Unknown")")
            }
        })

        // 配置视图上下文
        container.viewContext.automaticallyMergesChangesFromParent = true
    }

    /// 重置数据库（删除现有数据库文件）
    static func resetDatabase() {
        // 获取默认的数据库文件URL
        let container = NSPersistentContainer(name: "ztt2")
        guard let storeURL = container.persistentStoreDescriptions.first?.url else {
            print("无法获取数据库文件URL")
            return
        }

        do {
            // 删除数据库文件
            if FileManager.default.fileExists(atPath: storeURL.path) {
                try FileManager.default.removeItem(at: storeURL)
                print("已删除数据库文件: \(storeURL.path)")
            }

            // 删除相关的辅助文件
            let walURL = storeURL.appendingPathExtension("sqlite-wal")
            let shmURL = storeURL.appendingPathExtension("sqlite-shm")

            if FileManager.default.fileExists(atPath: walURL.path) {
                try FileManager.default.removeItem(at: walURL)
                print("已删除WAL文件")
            }

            if FileManager.default.fileExists(atPath: shmURL.path) {
                try FileManager.default.removeItem(at: shmURL)
                print("已删除SHM文件")
            }

            print("数据库文件已删除，应用重启后将重新创建")

        } catch {
            print("删除数据库文件失败: \(error)")
            fatalError("无法重置数据库: \(error)")
        }
    }
}

// MARK: - 数据操作扩展
extension PersistenceController {

    /// 保存上下文
    func save() {
        let context = container.viewContext

        if context.hasChanges {
            do {
                try context.save()
                print("Core Data保存成功")
            } catch {
                let nsError = error as NSError
                print("Save error: \(nsError), \(nsError.userInfo)")

                // 如果是权限错误，提供更详细的信息
                if nsError.code == 513 {
                    print("权限错误详情:")
                    print("- 错误代码: \(nsError.code)")
                    print("- 错误域: \(nsError.domain)")
                    print("- 用户信息: \(nsError.userInfo)")
                    print("建议：删除应用重新安装，或清理模拟器数据")
                }
            }
        } else {
            print("Core Data没有变化，无需保存")
        }
    }

    /// 获取当前用户
    func getCurrentUser() -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.fetchLimit = 1

        do {
            let users = try container.viewContext.fetch(request)
            return users.first
        } catch {
            print("Failed to fetch user: \(error)")
            return nil
        }
    }

    /// 创建默认用户（如果不存在）
    func createDefaultUserIfNeeded() -> User {
        if let existingUser = getCurrentUser() {
            return existingUser
        }

        let user = User(context: container.viewContext)
        user.id = UUID()
        user.nickname = "家长"
        user.createdAt = Date()

        // 创建默认订阅
        let subscription = Subscription(context: container.viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        save()
        return user
    }
}
